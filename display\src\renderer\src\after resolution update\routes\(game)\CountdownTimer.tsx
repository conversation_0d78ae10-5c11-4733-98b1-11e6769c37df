import { createFileRoute } from "@tanstack/react-router";
import React, { useState, useEffect, useCallback } from "react";
import { formatTime } from "./GameUtils";

interface CountdownTimerProps {
  initialSeconds: number;
  onComplete?: () => void;
  isServerDown?: boolean;
}

function CountdownTimer({
  initialSeconds,
  onComplete,
  isServerDown = false,
}: CountdownTimerProps) {
  const [seconds, setSeconds] = useState(initialSeconds);
  const [isActive, setIsActive] = useState(true);

  const reset = useCallback(() => {
    setSeconds(initialSeconds);
    setIsActive(true);
  }, [initialSeconds]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive) {
      interval = setInterval(() => {
        setSeconds((prevSeconds) => {
          if (prevSeconds <= 1) {
            setIsActive(false);
            if (onComplete) onComplete();

            // If server is down, reset timer to simulate continuous gameplay
            if (isServerDown) {
              setTimeout(() => {
                reset();
              }, 1000); // Wait 1 second before resetting
              return 0;
            }

            return 0;
          }
          return prevSeconds - 1;
        });
      }, 1000);
    } else if (!isActive && interval) {
      clearInterval(interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, onComplete, reset, isServerDown]);

  // Format the time as MM:SS
  const formattedTime = formatTime(seconds);

  return (
    <div className="countdown-timer">
      <div className="timer-display text-4xl font-bold">{formattedTime}</div>
    </div>
  );
}

// Default component for the route
function CountdownTimerRoute() {
  return (
    <div className="p-4">
      <h1 className="mb-4 text-2xl font-bold">Countdown Timer</h1>
      <CountdownTimer
        initialSeconds={210}
        onComplete={() => console.log("Countdown complete")}
      />
    </div>
  );
}

export { CountdownTimer };

export const Route = createFileRoute("/(game)/CountdownTimer")({
  component: CountdownTimerRoute,
});
