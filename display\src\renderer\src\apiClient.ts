import axios from "axios";
import localstore from "./utils/localstore";

const BASE_URL = `${import.meta.env.VITE_BACKEND_API_URL}`;

export const apiClient = axios.create({
  baseURL: BASE_URL,
  withCredentials: true,
});

// Add a request interceptor to include the access token in the headers
apiClient.interceptors.request.use((config) => {
  const access_token = localstore.getToken();
  if (access_token) {
    config.headers["Authorization"] = `Bearer ${access_token}`;
  }
  return config;
});

// Add a response interceptor to handle token refreshing
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    if (error.response.status === 403) {
      // originalRequest._retry = true;
      try {
        const response = await axios.get(`${BASE_URL}/auth/refresh-token`, {
          withCredentials: true,
        });
        const { access_token } = response.data;
        localstore.authenticateUser(access_token);
        apiClient.defaults.headers["Authorization"] = `Bearer ${access_token}`;
        return apiClient(originalRequest);
      } catch (err) {
        localstore.deauthenticateUser();
        return Promise.reject(err);
      }
    }
    return Promise.reject(error);
  },
);
