import { z } from "zod";

export const authSchema = z.object({
  _id: z.string(),
  name: z.string(),
  username: z.string(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  agent: z.string().optional(),
  configuration: z.object({
    commissionPercent: z.number(),
    minBetAmount: z.number(),
    maxBetAmount: z.number(),
    profitThresholdPercent: z.number(),
    minRtpPercent: z.number(),
    profitWindowDays: z.number(),
    roundDuration: z.number(),
    startEventNumber: z.number(),
  }),
  isActive: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type TAuthUser = z.infer<typeof authSchema>;
