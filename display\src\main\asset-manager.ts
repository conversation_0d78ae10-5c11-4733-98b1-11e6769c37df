import { protocol } from 'electron';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { AssetsManifest, BundledAsset } from './assets-types';

class AssetManager {
  private manifest: AssetsManifest | null = null;
  private bundleData: Buffer | null = null;
  private cache: Map<string, Buffer> = new Map();
  private bundlePath: string;
  private manifestPath: string;

  constructor() {
    // Check if we're in development mode by looking at the resourcesPath
    // In development, resourcesPath points to electron's resources, not our app
    const isDev = process.resourcesPath && process.resourcesPath.includes('node_modules');

    if (isDev) {
      // Development mode - assets are in the src/main directory
      const srcMainPath = join(__dirname, '../../src/main');
      this.bundlePath = join(srcMainPath, 'assets.bundle');
      this.manifestPath = join(srcMainPath, 'assets-manifest.json');
    } else {
      // Production mode - assets are unpacked from asar
      const basePath = join(process.resourcesPath, 'app.asar.unpacked', 'out', 'main');
      this.bundlePath = join(basePath, 'assets.bundle');
      this.manifestPath = join(basePath, 'assets-manifest.json');
    }

  }

  /**
   * Initialize the asset manager by loading the bundled assets
   */
  initialize(): boolean {
    try {
      // Check if manifest file exists
      if (!existsSync(this.manifestPath)) {
        return false;
      }

      // Load manifest
      const manifestData = readFileSync(this.manifestPath, 'utf8');
      this.manifest = JSON.parse(manifestData);

      if (!this.manifest) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Load bundle data if not already loaded
   */
  private loadBundleData(): Buffer | null {
    if (!this.bundleData) {
      try {
        if (!existsSync(this.bundlePath)) {
          return null;
        }
        this.bundleData = readFileSync(this.bundlePath);
      } catch (error) {
        return null;
      }
    }
    return this.bundleData;
  }

  /**
   * Get an asset by its original path
   */
  getAsset(originalPath: string): Buffer | null {
    try {
      // Normalize path
      const normalizedPath = originalPath.replace(/\\/g, '/').replace(/^\//, '');

      // Check cache first
      if (this.cache.has(normalizedPath)) {
        return this.cache.get(normalizedPath)!;
      }

      if (!this.manifest) {
        return null;
      }

      const assetInfo = this.manifest.assets[normalizedPath];
      if (!assetInfo) {
        return null;
      }

      // Load bundle data
      const bundleData = this.loadBundleData();
      if (!bundleData) {
        return null;
      }

      // Extract asset data from bundle
      const assetData = bundleData.subarray(assetInfo.offset, assetInfo.offset + assetInfo.length);

      // Cache the asset
      this.cache.set(normalizedPath, assetData);

      return assetData;
    } catch (error) {
      console.error('Error getting asset:', originalPath, error);
      return null;
    }
  }

  /**
   * Get MIME type for an asset (now stored in the asset info)
   */
  private getMimeType(assetInfo: BundledAsset): string {
    return assetInfo.mimeType || 'application/octet-stream';
  }

  /**
   * Register the custom protocol for assets
   */
  registerProtocol(): void {
    try {
      protocol.registerBufferProtocol('asset', (request, callback) => {
        try {

          // Extract the asset path from the URL
          const url = new URL(request.url);
          let assetPath = url.pathname;

          // Remove leading slash and add images/ prefix if not present
          assetPath = assetPath.replace(/^\//, '');
          if (!assetPath.startsWith('images/') && !assetPath.startsWith('videos/') && !assetPath.startsWith('audio/') && !assetPath.startsWith('fonts/')) {
            assetPath = 'images/' + assetPath;
          }


          // Get the asset data
          const assetData = this.getAsset(assetPath);

          if (!assetData) {
            callback({ error: -6 }); // net::ERR_FILE_NOT_FOUND
            return;
          }

          // Get asset info for MIME type
          const normalizedPath = assetPath.replace(/\\/g, '/').replace(/^\//, '');
          const assetInfo = this.manifest?.assets[normalizedPath];
          const mimeType = assetInfo ? this.getMimeType(assetInfo) : 'application/octet-stream';


          callback({
            data: assetData,
            mimeType: mimeType,
            headers: {
              'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
              'Access-Control-Allow-Origin': '*'
            }
          });
        } catch (error) {
          callback({ error: -2 }); // net::ERR_FAILED
        }
      });

    } catch (error) {
    }
  }

  /**
   * Clear the asset cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

export const assetManager = new AssetManager();
