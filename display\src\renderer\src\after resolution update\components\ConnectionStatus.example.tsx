import React from 'react';
import { useSocketIO, ConnectionStatus } from '../contexts/SocketIOContext';

/**
 * Example component demonstrating how to use the enhanced SocketIO connection health features
 * This component shows real-time connection status, health metrics, and provides manual controls
 */
export const ConnectionStatusExample: React.FC = () => {
  const {
    connectionStatus,
    connectionHealth,
    isConnected,
    isServerDown,
    reconnectSocket,
    forceReconnect,
    getConnectionStatusText,
  } = useSocketIO();

  const getStatusColor = (status: ConnectionStatus) => {
    switch (status) {
      case ConnectionStatus.CONNECTED:
        return connectionHealth.isStable ? '#10B981' : '#F59E0B'; // Green or Yellow
      case ConnectionStatus.CONNECTING:
      case ConnectionStatus.RECONNECTING:
        return '#3B82F6'; // Blue
      case ConnectionStatus.DISCONNECTED:
        return '#6B7280'; // Gray
      case ConnectionStatus.FAILED:
      case ConnectionStatus.SERVER_DOWN:
        return '#EF4444'; // Red
      default:
        return '#6B7280';
    }
  };

  const formatTimestamp = (timestamp: number | null) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatLatency = (latency: number | null) => {
    if (latency === null) return 'Unknown';
    return `${latency}ms`;
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md max-w-md">
      <h3 className="text-lg font-semibold mb-4">Connection Status</h3>
      
      {/* Main Status Indicator */}
      <div className="flex items-center mb-4">
        <div 
          className="w-3 h-3 rounded-full mr-3"
          style={{ backgroundColor: getStatusColor(connectionStatus) }}
        />
        <span className="font-medium">{getConnectionStatusText()}</span>
      </div>

      {/* Connection Health Metrics */}
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Status:</span>
          <span className="font-medium">{connectionStatus}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Stable:</span>
          <span className={connectionHealth.isStable ? 'text-green-600' : 'text-yellow-600'}>
            {connectionHealth.isStable ? 'Yes' : 'No'}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Latency:</span>
          <span>{formatLatency(connectionHealth.latency)}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Last Connected:</span>
          <span>{formatTimestamp(connectionHealth.lastConnected)}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Last Disconnected:</span>
          <span>{formatTimestamp(connectionHealth.lastDisconnected)}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Reconnect Attempts:</span>
          <span>{connectionHealth.reconnectAttempts}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Consecutive Failures:</span>
          <span className={connectionHealth.consecutiveFailures > 0 ? 'text-red-600' : 'text-green-600'}>
            {connectionHealth.consecutiveFailures}
          </span>
        </div>
      </div>

      {/* Legacy Status (for backward compatibility) */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Legacy Status</h4>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Is Connected:</span>
            <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
              {isConnected ? 'Yes' : 'No'}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Server Down:</span>
            <span className={isServerDown ? 'text-red-600' : 'text-green-600'}>
              {isServerDown ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      </div>

      {/* Manual Controls */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Manual Controls</h4>
        <div className="space-y-2">
          <button
            onClick={reconnectSocket}
            disabled={connectionStatus === ConnectionStatus.CONNECTING || connectionStatus === ConnectionStatus.RECONNECTING}
            className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Reconnect
          </button>
          
          <button
            onClick={forceReconnect}
            disabled={connectionStatus === ConnectionStatus.CONNECTING}
            className="w-full px-3 py-2 text-sm bg-orange-500 text-white rounded hover:bg-orange-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Force Reconnect
          </button>
        </div>
      </div>

      {/* Connection Tips */}
      {connectionHealth.consecutiveFailures > 3 && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm text-yellow-800">
            Multiple connection failures detected. Check your internet connection or try force reconnect.
          </p>
        </div>
      )}
      
      {connectionHealth.latency && connectionHealth.latency > 1000 && (
        <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded">
          <p className="text-sm text-orange-800">
            High latency detected ({connectionHealth.latency}ms). Connection may be unstable.
          </p>
        </div>
      )}
    </div>
  );
};

export default ConnectionStatusExample;
