/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as NetworkReconnectImport } from './routes/network-reconnect'
import { Route as AuthRouteImport } from './routes/auth/route'
import { Route as gameRouteImport } from './routes/(game)/route'
import { Route as gameIndexImport } from './routes/(game)/index'
import { Route as AuthLoginImport } from './routes/auth/login'
import { Route as gameServerStatusMonitorImport } from './routes/(game)/ServerStatusMonitor'

// Create/Update Routes

const NetworkReconnectRoute = NetworkReconnectImport.update({
  id: '/network-reconnect',
  path: '/network-reconnect',
  getParentRoute: () => rootRoute,
} as any)

const AuthRouteRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const gameRouteRoute = gameRouteImport.update({
  id: '/(game)',
  getParentRoute: () => rootRoute,
} as any)

const gameIndexRoute = gameIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => gameRouteRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRouteRoute,
} as any)

const gameServerStatusMonitorRoute = gameServerStatusMonitorImport.update({
  id: '/ServerStatusMonitor',
  path: '/ServerStatusMonitor',
  getParentRoute: () => gameRouteRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/(game)': {
      id: '/(game)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof gameRouteImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRoute
    }
    '/network-reconnect': {
      id: '/network-reconnect'
      path: '/network-reconnect'
      fullPath: '/network-reconnect'
      preLoaderRoute: typeof NetworkReconnectImport
      parentRoute: typeof rootRoute
    }
    '/(game)/ServerStatusMonitor': {
      id: '/(game)/ServerStatusMonitor'
      path: '/ServerStatusMonitor'
      fullPath: '/ServerStatusMonitor'
      preLoaderRoute: typeof gameServerStatusMonitorImport
      parentRoute: typeof gameRouteImport
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof AuthRouteImport
    }
    '/(game)/': {
      id: '/(game)/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof gameIndexImport
      parentRoute: typeof gameRouteImport
    }
  }
}

// Create and export the route tree

interface gameRouteRouteChildren {
  gameServerStatusMonitorRoute: typeof gameServerStatusMonitorRoute
  gameIndexRoute: typeof gameIndexRoute
}

const gameRouteRouteChildren: gameRouteRouteChildren = {
  gameServerStatusMonitorRoute: gameServerStatusMonitorRoute,
  gameIndexRoute: gameIndexRoute,
}

const gameRouteRouteWithChildren = gameRouteRoute._addFileChildren(
  gameRouteRouteChildren,
)

interface AuthRouteRouteChildren {
  AuthLoginRoute: typeof AuthLoginRoute
}

const AuthRouteRouteChildren: AuthRouteRouteChildren = {
  AuthLoginRoute: AuthLoginRoute,
}

const AuthRouteRouteWithChildren = AuthRouteRoute._addFileChildren(
  AuthRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof gameIndexRoute
  '/auth': typeof AuthRouteRouteWithChildren
  '/network-reconnect': typeof NetworkReconnectRoute
  '/ServerStatusMonitor': typeof gameServerStatusMonitorRoute
  '/auth/login': typeof AuthLoginRoute
}

export interface FileRoutesByTo {
  '/auth': typeof AuthRouteRouteWithChildren
  '/network-reconnect': typeof NetworkReconnectRoute
  '/ServerStatusMonitor': typeof gameServerStatusMonitorRoute
  '/auth/login': typeof AuthLoginRoute
  '/': typeof gameIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/(game)': typeof gameRouteRouteWithChildren
  '/auth': typeof AuthRouteRouteWithChildren
  '/network-reconnect': typeof NetworkReconnectRoute
  '/(game)/ServerStatusMonitor': typeof gameServerStatusMonitorRoute
  '/auth/login': typeof AuthLoginRoute
  '/(game)/': typeof gameIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/auth'
    | '/network-reconnect'
    | '/ServerStatusMonitor'
    | '/auth/login'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/auth'
    | '/network-reconnect'
    | '/ServerStatusMonitor'
    | '/auth/login'
    | '/'
  id:
    | '__root__'
    | '/(game)'
    | '/auth'
    | '/network-reconnect'
    | '/(game)/ServerStatusMonitor'
    | '/auth/login'
    | '/(game)/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  gameRouteRoute: typeof gameRouteRouteWithChildren
  AuthRouteRoute: typeof AuthRouteRouteWithChildren
  NetworkReconnectRoute: typeof NetworkReconnectRoute
}

const rootRouteChildren: RootRouteChildren = {
  gameRouteRoute: gameRouteRouteWithChildren,
  AuthRouteRoute: AuthRouteRouteWithChildren,
  NetworkReconnectRoute: NetworkReconnectRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/(game)",
        "/auth",
        "/network-reconnect"
      ]
    },
    "/(game)": {
      "filePath": "(game)/route.tsx",
      "children": [
        "/(game)/ServerStatusMonitor",
        "/(game)/"
      ]
    },
    "/auth": {
      "filePath": "auth/route.tsx",
      "children": [
        "/auth/login"
      ]
    },
    "/network-reconnect": {
      "filePath": "network-reconnect.tsx"
    },
    "/(game)/ServerStatusMonitor": {
      "filePath": "(game)/ServerStatusMonitor.tsx",
      "parent": "/(game)"
    },
    "/auth/login": {
      "filePath": "auth/login.tsx",
      "parent": "/auth"
    },
    "/(game)/": {
      "filePath": "(game)/index.tsx",
      "parent": "/(game)"
    }
  }
}
ROUTE_MANIFEST_END */
