import { useState, useEffect, useCallback } from 'react';

interface UseAssetOptions {
  preload?: boolean;
  fallback?: string;
}

interface UseAssetReturn {
  url: string | null;
  loading: boolean;
  error: string | null;
  reload: () => void;
}

/**
 * Hook for loading embedded assets
 * @param assetPath - The original asset path (e.g., "images/logo.png")
 * @param options - Configuration options
 * @returns Object with url, loading state, error, and reload function
 */
export function useAsset(
  assetPath: string | null,
  options: UseAssetOptions = {}
): UseAssetReturn {
  const [url, setUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAsset = useCallback(async () => {
    if (!assetPath) {
      setUrl(null);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Always use embedded asset protocol first
      const embeddedUrl = window.api.getAssetUrl(assetPath);
      console.log("🔗 useAsset generated URL:", { assetPath, embeddedUrl });
      setUrl(embeddedUrl);
      setLoading(false);
    } catch (err) {
      console.error('Failed to load embedded asset:', assetPath, err);
      setError(err instanceof Error ? err.message : 'Failed to load asset');

      // Use fallback if provided
      if (options.fallback) {
        setUrl(options.fallback);
      } else {
        setUrl(null);
      }
      setLoading(false);
    }
  }, [assetPath, options.fallback]);

  const reload = useCallback(() => {
    loadAsset();
  }, [loadAsset]);

  useEffect(() => {
    if (options.preload || assetPath) {
      loadAsset();
    }
  }, [loadAsset, options.preload, assetPath]);

  return {
    url,
    loading,
    error,
    reload
  };
}

/**
 * Hook for preloading multiple embedded assets
 * @param assetPaths - Array of asset paths to preload
 * @returns Object with loading state and preload function
 */
export function usePreloadAssets(assetPaths: string[]) {
  const [loading, setLoading] = useState(false);
  const [loadedCount, setLoadedCount] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);

  const preloadAssets = useCallback(async () => {
    if (assetPaths.length === 0) return;

    setLoading(true);
    setLoadedCount(0);
    setErrors([]);

    const promises = assetPaths.map(async (assetPath, index) => {
      try {
        // Always use embedded asset protocol
        const embeddedUrl = window.api.getAssetUrl(assetPath);

        // For images and videos, we can preload by creating elements
        const extension = assetPath.split('.').pop()?.toLowerCase();
        if (['png', 'jpg', 'jpeg', 'gif'].includes(extension || '')) {
          const img = new Image();
          img.src = embeddedUrl;
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
          });
        } else if (['mp4', 'webm'].includes(extension || '')) {
          const video = document.createElement('video');
          video.src = embeddedUrl;
          video.preload = 'metadata';
          await new Promise((resolve, reject) => {
            video.onloadedmetadata = resolve;
            video.onerror = reject;
          });
        }

        setLoadedCount(prev => prev + 1);
      } catch (err) {
        console.error(`Failed to preload asset ${assetPath}:`, err);
        setErrors(prev => [...prev, `${assetPath}: ${err instanceof Error ? err.message : 'Unknown error'}`]);
      }
    });

    await Promise.allSettled(promises);
    setLoading(false);
  }, [assetPaths]);

  useEffect(() => {
    preloadAssets();
  }, [preloadAssets]);

  return {
    loading,
    loadedCount,
    totalCount: assetPaths.length,
    errors,
    preloadAssets
  };
}

/**
 * Utility function to get embedded asset URL directly
 * @param assetPath - The original asset path
 * @returns The embedded asset URL or fallback
 */
export function getAssetUrl(assetPath: string): string {
  // Always use embedded asset protocol
  return window.api.getAssetUrl(assetPath);
}

/**
 * Utility function to create a CSS background image URL for embedded assets
 * @param assetPath - The original asset path
 * @returns CSS url() string for embedded asset
 */
export function getAssetCssUrl(assetPath: string): string {
  const url = getAssetUrl(assetPath);
  return `url("${url}")`;
}
