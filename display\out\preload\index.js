"use strict";
const electron = require("electron");
const preload = require("@electron-toolkit/preload");
const api = {
  /**
   * Get the URL for an embedded asset
   * @param assetPath - The original asset path (e.g., "images/logo.png")
   * @returns The embedded asset URL
   */
  getAssetUrl: (assetPath) => {
    const normalizedPath = assetPath.replace(/^\//, "");
    return `embedded-asset://${normalizedPath}`;
  },
  // Keep backward compatibility
  getEncryptedAssetUrl: (assetPath) => {
    const normalizedPath = assetPath.replace(/^\//, "");
    return `embedded-asset://${normalizedPath}`;
  },
  // Auth store methods
  authStore: {
    set: (key, value) => electron.ipcRenderer.invoke("auth-store-set", key, value),
    get: (key) => electron.ipcRenderer.invoke("auth-store-get", key),
    has: (key) => electron.ipcRenderer.invoke("auth-store-has", key),
    delete: (key) => electron.ipcRenderer.invoke("auth-store-delete", key)
  }
};
if (process.contextIsolated) {
  try {
    electron.contextBridge.exposeInMainWorld("electron", preload.electronAPI);
    electron.contextBridge.exposeInMainWorld("api", api);
  } catch (error) {
    console.error(error);
  }
} else {
  window.electron = preload.electronAPI;
  window.api = api;
}
