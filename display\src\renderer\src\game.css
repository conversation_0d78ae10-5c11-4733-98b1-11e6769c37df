.App {
  width: 100vw;
  height: 100vh;
  background: url("./assets/images/vidbg.png") no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.NumberAppContainer {
  display: flex;
  width: 100%;
}
.leftSideBar {
  position: relative;
  width: 58% !important;
  padding: 0.2vw 1.2vw 0 3.5vw;
}
.numbers {
  width: 100%;
}
.darkOrange {
  color: #f49119;
}
.text-white {
  color: #fff;
}
.Eurostib {
  font-family: "Eurostib";
}
.text-error {
  color: #ec3118;
  /* font-family: Goodtimes; */
}
.text-center {
  text-align: center;
}
.uppercase {
  text-transform: uppercase;
}

.HourNumberImage {
  height: 5.6vw;
  object-fit: cover;
}

.drawTextImage {
  /* width: 16vw; */
  height: 8vh;
  object-fit: cover;
}
.text-darkOrange {
  color: #f49119;
}

.info {
  height: 100vh;
  width: 42% !important;
  color: #fff;
  padding-top: 2.5rem;
  background: linear-gradient(210deg, #850c02 -29.09%, #310400 51.77%);
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #09d3ac;
}

.about {
  margin-top: 10%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

.keno {
  color: #fff;
  mix-blend-mode: soft-light !important;
  font-size: 4vw;
}
.candidateNumber {
  font-family: "Eurostib";
}

.bg-bgRedDark {
  background-image: url("./assets/images/red.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  color: rgb(255, 26, 0) !important;
}
.head-active {
  background-image: url("./assets/images/yellow.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;

  color: #000 !important;
}
.tail-active {
  background-image: url("./assets/images/orange.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  color: #000 !important;
}

.numberContainer {
  width: 100%;
}

.mt-1 {
  margin-top: 0.8rem;
}

.space-x-1 {
  margin-left: 0.25rem; /* 4px */
}
.space-x-2 {
  margin-left: 0.5rem; /* 8px */
}
.main {
  width: 100%;
}
.space-y-1 {
  margin-top: 0.25rem; /* 4px */
}
.w-full {
  width: 100%;
}
.text-black {
  color: #333;
}
.gap-10 {
  gap: 2.5rem; /* 40px */
}
.gap-x-2 {
  column-gap: 0.5rem !important; /* 8px */
}
.gap-x-1 {
  column-gap: 0.25rem !important; /* 8px */
}
.gap-10 {
  gap: 1.25rem; /* 20px */
}
.my-1 {
  margin-top: 0.6rem;
  margin-bottom: 0.6rem;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-between {
  justify-content: space-between;
}
.shadow-md {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.opacity-60 {
  opacity: 0.6;
}
.rounded-md {
  border-radius: 0.575rem; /* 6px */
}
.rounded {
  border-radius: 0.25rem; /* 4px */
}
button {
  background-color: white;
  color: black;
  padding: 10px 20px;
  border-radius: 10px;
  font-size: 1.3rem;
  box-shadow:
    0px 8px 28px -6px rgba(24, 39, 75, 0.12),
    0px 18px 88px -4px rgba(24, 39, 75, 0.14);
  transition: all ease-in 0.1s;
  cursor: pointer;
  opacity: 0.9;
}
.px-5 {
  padding-left: 1.5rem; /* 24px */
  padding-right: 1.5rem; /* 24px */
}
.button-even {
  background-image: url("./assets/images/9even.png");
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0.5rem 3rem;
  text-align: center;
  margin-left: 5px;
}
.button-head {
  /* background-image: url("././assets/images/9head.png");
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0.5rem 3rem; */
  text-align: center;
  margin-left: 5px;
  color: black;
}
.button-tail {
  /* background-image: url("././assets/images/9tail.png");
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0.5rem 3.5rem; */
  text-align: center;
  color: black;
}

.tailContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.drawingVideoContainer {
  position: relative;
  width: 42% !important;
  
  align-items: flex-end;
  /* background-color: green; */
  /* left */
  height: 100vh;
  /* background: url("./assets/images/img_0.png") no-repeat center center fixed; */
  -webkit-background-size: contain;
  -moz-background-size: contain;
  -o-background-size: contain;
  background-size: contain;
  z-index: 50;
  overflow: hidden;
}

.drawenNumbers {
  font-family: "Eurostib";
  color: white;
  position: absolute;
  top: 7%;
  right: 0%;
  font-weight: bold;
}

.history {
  width: 100vw;
  height: 100vh;
  background: url("./assets/images/historybg.png") no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  /* margin: 0; */
  padding-left: 4vw;
  padding-right: 4vw;
  overflow: hidden;
}

.text-shadow {
  text-shadow: -3px 0px 10px black;
}

.blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 0;
  }
  60% {
    opacity: 0;
  }
  90% {
    opacity: 1;
  }
}



.last-number {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.8) !important;
  border: 2px solid white;
}

.animated-number {
  position: absolute !important;
  overflow: visible !important;
  transform-origin: center center !important;
  z-index: 9999 !important;
}

@keyframes bounce {
  0% { transform: scale(1.8); }
  15% { transform: scale(1.3); }
  30% { transform: scale(1.6); }
  45% { transform: scale(1.1); }
  55% { transform: scale(1.2); }
  65% { transform: scale(1.0); }
  70% { transform: scale(1.03); }
  80% { transform: scale(1.0); }
}


.animate-visibility {
  animation: smooth-disappear 1s infinite;
}

@keyframes smooth-disappear {
  0% { visibility: visible; }
  20% {visibility: hidden;}
  80% { visibility: hidden; }
  100% { visibility: visible; }
}