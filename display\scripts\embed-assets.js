const fs = require("fs");
const path = require("path");

// Directories
const PUBLIC_DIR = path.join(__dirname, "../public");

// Read a file and return its data
function readFile(filePath) {
  try {
    const data = fs.readFileSync(filePath);
    return {
      success: true,
      data: data,
      size: data.length,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

// Get all asset files recursively
function getAllAssets(dir, baseDir = dir) {
  const assets = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      assets.push(...getAllAssets(fullPath, baseDir));
    } else {
      // Only include specific asset types
      const ext = path.extname(item).toLowerCase();
      if (
        [
          ".png",
          ".jpg",
          ".jpeg",
          ".gif",
          ".mp4",
          ".mp3",
          ".wav",
          ".ogg",
          ".ttf",
          ".otf",
          ".woff",
          ".woff2",
        ].includes(ext)
      ) {
        const relativePath = path.relative(baseDir, fullPath);
        assets.push({
          originalPath: fullPath,
          relativePath: relativePath.replace(/\\/g, "/"), // Normalize path separators
          filename: item,
          extension: ext,
          size: stat.size,
        });
      }
    }
  }

  return assets;
}

// Get MIME type from file extension
function getMimeTypeFromExtension(extension) {
  const mimeTypes = {
    ".png": "image/png",
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".gif": "image/gif",
    ".mp4": "video/mp4",
    ".mp3": "audio/mpeg",
    ".wav": "audio/wav",
    ".ogg": "audio/ogg",
    ".ttf": "font/ttf",
    ".otf": "font/otf",
    ".woff": "font/woff",
    ".woff2": "font/woff2",
  };
  return mimeTypes[extension.toLowerCase()] || "application/octet-stream";
}

// Generate TypeScript file with embedded assets using binary bundle approach
function generateEmbeddedAssetsTS(embeddedAssets) {
  // Create a binary bundle file containing all asset data
  const bundlePath = path.join(__dirname, "../src/main/embedded-assets.bundle");
  const manifestPath = path.join(
    __dirname,
    "../src/main/embedded-assets-manifest.json",
  );

  // Create the bundle by concatenating all asset data
  let bundleBuffer = Buffer.alloc(0);
  let currentOffset = 0;

  // Update asset info with offsets in the bundle
  const bundleManifest = {
    version: embeddedAssets.version,
    timestamp: embeddedAssets.timestamp,
    assets: {},
  };

  for (const [assetPath, assetInfo] of Object.entries(embeddedAssets.assets)) {
    const assetData = Buffer.from(assetInfo.data, "base64");

    // Add to bundle
    bundleBuffer = Buffer.concat([bundleBuffer, assetData]);

    // Update manifest with bundle offset
    bundleManifest.assets[assetPath] = {
      offset: currentOffset,
      length: assetData.length,
      size: assetInfo.size,
      extension: assetInfo.extension,
      mimeType: assetInfo.mimeType,
    };

    currentOffset += assetData.length;
  }

  // Write the binary bundle
  fs.writeFileSync(bundlePath, bundleBuffer);

  // Write the manifest as JSON
  fs.writeFileSync(manifestPath, JSON.stringify(bundleManifest, null, 2));

  console.log(
    `📦 Created binary bundle: ${(bundleBuffer.length / 1024 / 1024).toFixed(2)} MB`,
  );
  console.log(`📄 Bundle path: ${bundlePath}`);
  console.log(`📄 Manifest path: ${manifestPath}`);

  return `// Auto-generated file - DO NOT EDIT
// Generated on: ${embeddedAssets.timestamp}
// Total assets: ${Object.keys(embeddedAssets.assets).length}

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

export interface EmbeddedAsset {
  offset: number;
  length: number;
  size: number;
  extension: string;
  mimeType: string;
}

export interface EmbeddedAssetsManifest {
  version: string;
  timestamp: string;
  assets: { [path: string]: EmbeddedAsset };
}

// Load manifest and bundle data
let MANIFEST: EmbeddedAssetsManifest | null = null;
let BUNDLE_DATA: Buffer | null = null;

function loadEmbeddedAssets(): boolean {
  if (MANIFEST && BUNDLE_DATA) {
    return true;
  }

  try {
    // Determine paths based on environment
    const isDev = process.resourcesPath && process.resourcesPath.includes('node_modules');

    let manifestPath: string;
    let bundlePath: string;

    if (isDev) {
      // Development mode
      const srcMainPath = join(__dirname, '../../src/main');
      manifestPath = join(srcMainPath, 'embedded-assets-manifest.json');
      bundlePath = join(srcMainPath, 'embedded-assets.bundle');
    } else {
      // Production mode
      const basePath = join(process.resourcesPath, 'app.asar.unpacked', 'out', 'main');
      manifestPath = join(basePath, 'embedded-assets-manifest.json');
      bundlePath = join(basePath, 'embedded-assets.bundle');
    }

    if (!existsSync(manifestPath) || !existsSync(bundlePath)) {
      console.error('Embedded assets not found:', { manifestPath, bundlePath });
      return false;
    }

    MANIFEST = JSON.parse(readFileSync(manifestPath, 'utf8'));
    BUNDLE_DATA = readFileSync(bundlePath);

    console.log(\`✅ Loaded embedded assets: \${Object.keys(MANIFEST.assets).length} assets, \${(BUNDLE_DATA.length / 1024 / 1024).toFixed(2)} MB\`);
    return true;
  } catch (error) {
    console.error('Failed to load embedded assets:', error);
    return false;
  }
}

export function getEmbeddedAssetData(assetPath: string): Buffer | null {
  if (!loadEmbeddedAssets() || !MANIFEST || !BUNDLE_DATA) {
    return null;
  }

  const normalizedPath = assetPath.replace(/\\\\\\\\/g, '/').replace(/^\\\//, '');
  const assetInfo = MANIFEST.assets[normalizedPath];

  if (!assetInfo) {
    return null;
  }

  return BUNDLE_DATA.subarray(assetInfo.offset, assetInfo.offset + assetInfo.length);
}

export const EMBEDDED_ASSETS: EmbeddedAssetsManifest = {
  get version() { loadEmbeddedAssets(); return MANIFEST?.version || ''; },
  get timestamp() { loadEmbeddedAssets(); return MANIFEST?.timestamp || ''; },
  get assets() { loadEmbeddedAssets(); return MANIFEST?.assets || {}; }
};
`;
}

// Main embedding process
function embedAssets() {
  console.log("📦 Starting asset embedding process...");

  // Get all assets
  const assets = getAllAssets(PUBLIC_DIR);
  console.log(`📁 Found ${assets.length} assets to embed`);

  const embeddedAssets = {
    version: "1.0.0",
    timestamp: new Date().toISOString(),
    assets: {},
  };

  let successCount = 0;
  let errorCount = 0;
  let totalSize = 0;

  // Read each asset and embed data directly
  for (const asset of assets) {
    console.log(`📁 Reading and embedding: ${asset.relativePath}`);

    const result = readFile(asset.originalPath);

    if (result.success) {
      // Store asset data directly as base64
      embeddedAssets.assets[asset.relativePath] = {
        data: result.data.toString("base64"),
        size: result.size,
        extension: asset.extension,
        mimeType: getMimeTypeFromExtension(asset.extension),
      };

      totalSize += result.size;
      successCount++;
      console.log(`✅ Embedded: ${asset.relativePath} (${result.size} bytes)`);
    } else {
      errorCount++;
      console.error(
        `❌ Failed to read: ${asset.relativePath} - ${result.error}`,
      );
    }
  }

  // Create TypeScript file with embedded assets
  console.log("📝 Generating TypeScript file...");
  const tsContent = generateEmbeddedAssetsTS(embeddedAssets);
  const tsFilePath = path.join(__dirname, "../src/main/embedded-assets.ts");
  fs.writeFileSync(tsFilePath, tsContent);

  console.log("\n📊 Embedding Summary:");
  console.log(`✅ Successfully embedded: ${successCount} files`);
  console.log(`❌ Failed to embed: ${errorCount} files`);
  console.log(
    `📦 Total embedded size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`,
  );
  console.log(`📄 Embedded assets written to: ${tsFilePath}`);

  if (errorCount > 0) {
    process.exit(1);
  }

  console.log("🎉 Asset embedding completed successfully!");
  console.log("💡 Assets are now embedded directly in the executable!");
}

// Run the embedding process
if (require.main === module) {
  embedAssets();
}

module.exports = { embedAssets, readFile };
