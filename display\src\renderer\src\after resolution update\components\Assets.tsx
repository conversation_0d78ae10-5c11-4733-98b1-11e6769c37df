import React, { forwardRef } from "react";
import { useAsset } from "../hooks/useAsset";

interface AssetImageProps
  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Asset Image component that automatically handles embedded asset loading
 */
export const AssetImage = forwardRef<HTMLImageElement, AssetImageProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useAsset(src, { fallback });

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
      }
    }, [error, onLoadError]);

    if (loading) {
      return (
        <div
          className={`animate-pulse bg-gray-200 ${props.className || ""}`}
          style={props.style}
        >
          {/* Loading placeholder */}
        </div>
      );
    }

    if (!url) {
      return fallback ? (
        <img
          {...props}
          ref={ref}
          src={fallback}
          alt={props.alt || "Fallback image"}
        />
      ) : null;
    }

    return <img {...props} ref={ref} src={url} />;
  },
);

AssetImage.displayName = "AssetImage";

interface AssetVideoProps
  extends Omit<React.VideoHTMLAttributes<HTMLVideoElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Asset Video component that automatically handles embedded asset loading
 */
export const AssetVideo = forwardRef<HTMLVideoElement, AssetVideoProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useAsset(src, { fallback });
    const videoRef = React.useRef<HTMLVideoElement>(null);
    const [isReady, setIsReady] = React.useState(false);
    const [hasError, setHasError] = React.useState(false);

    // Combine refs
    React.useImperativeHandle(ref, () => videoRef.current!, []);

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
        setHasError(true);
      }
    }, [error, onLoadError]);

    React.useEffect(() => {
      const video = videoRef.current;
      if (!video || !url) return;

      const handleCanPlay = () => {
        setIsReady(true);
        setHasError(false);
      };

      const handleError = (e: Event) => {
        console.error("Video error:", e);
        setHasError(true);
        setIsReady(false);
        if (onLoadError) {
          onLoadError("Failed to load video");
        }
      };

      const handleLoadStart = () => {
        setIsReady(false);
        setHasError(false);
      };

      video.addEventListener("canplay", handleCanPlay);
      video.addEventListener("error", handleError);
      video.addEventListener("loadstart", handleLoadStart);

      return () => {
        video.removeEventListener("canplay", handleCanPlay);
        video.removeEventListener("error", handleError);
        video.removeEventListener("loadstart", handleLoadStart);
      };
    }, [url, onLoadError]);

    if (loading) {
      return (
        <div
          className={`animate-pulse bg-gray-200 ${props.className || ""}`}
          style={props.style}
        >
          {/* Loading placeholder */}
        </div>
      );
    }

    if (hasError && fallback) {
      return (
        <video {...props} ref={videoRef} src={fallback}>
          Your browser does not support the video tag.
        </video>
      );
    }

    if (!url && !fallback) {
      return (
        <div
          className={`bg-gray-300 flex items-center justify-center ${
            props.className || ""
          }`}
          style={props.style}
        >
          <span className="text-gray-600">Video not available</span>
        </div>
      );
    }

    return (
      <video {...props} ref={videoRef} src={url || fallback}>
        Your browser does not support the video tag.
      </video>
    );
  },
);

AssetVideo.displayName = "AssetVideo";

interface AssetAudioProps
  extends Omit<React.AudioHTMLAttributes<HTMLAudioElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Asset Audio component that automatically handles embedded asset loading
 */
export const AssetAudio = forwardRef<HTMLAudioElement, AssetAudioProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useAsset(src, { fallback });
    const audioRef = React.useRef<HTMLAudioElement>(null);

    // Combine refs
    React.useImperativeHandle(ref, () => audioRef.current!, []);

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
      }
    }, [error, onLoadError]);

    if (loading) {
      return (
        <div className="animate-pulse bg-gray-200 h-8 w-full rounded">
          {/* Loading placeholder for audio */}
        </div>
      );
    }

    if (!url) {
      if (fallback) {
        return <audio {...props} ref={audioRef} src={fallback} />;
      }
      return null;
    }

    // Filter out invalid props for audio element
    const audioProps = { ...props };
    delete (audioProps as any).onLoadError;

    return <audio {...audioProps} ref={audioRef} src={url} />;
  },
);

AssetAudio.displayName = "AssetAudio";

interface AssetBackgroundProps {
  src: string;
  fallback?: string;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onLoadError?: (error: string) => void;
}

/**
 * Component for embedded background images
 */
export const AssetBackground: React.FC<AssetBackgroundProps> = ({
  src,
  fallback,
  children,
  className = "",
  style = {},
  onLoadError,
}) => {
  const { url, loading, error } = useAsset(src, { fallback });

  React.useEffect(() => {
    if (error && onLoadError) {
      onLoadError(error);
    }
  }, [error, onLoadError]);

  const backgroundStyle: React.CSSProperties = {
    ...style,
    backgroundImage: url ? `url("${url}")` : undefined,
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
  };

  if (loading) {
    return (
      <div className={`animate-pulse bg-gray-200 ${className}`} style={style}>
        {children}
      </div>
    );
  }

  return (
    <div className={className} style={backgroundStyle}>
      {children}
    </div>
  );
};

/**
 * Higher-order component to wrap existing components with embedded asset support
 */
export function withAssets<T extends { src?: string }>(
  Component: React.ComponentType<T>,
) {
  return forwardRef<any, any>((props: any, ref: any) => {
    const { src, ...otherProps } = props;
    const { url, loading, error } = useAsset(src || null);
    const elementRef = React.useRef<any>(null);

    // Combine refs
    React.useImperativeHandle(ref, () => elementRef.current, []);

    // Handle loading state for video components
    const isVideoComponent = Component === "video" || 
      (typeof Component === "string" && Component === "video");

    if (loading) {
      return React.createElement("div", {
        className: "animate-pulse bg-gray-200",
        style: props.style,
        children: isVideoComponent ? "Loading video..." : "Loading...",
      });
    }

    if (error && isVideoComponent) {
      console.error("❌ [withAssets] Failed to load video:", src, error);
    }

    return React.createElement(Component, {
      ...otherProps,
      ref: elementRef,
      src: url || src,
    });
  });
}

// Pre-wrapped common components with proper typing
export const AssetImg = withAssets(
  "img" as any,
) as React.ForwardRefExoticComponent<
  React.ImgHTMLAttributes<HTMLImageElement> &
    React.RefAttributes<HTMLImageElement>
>;

export const AssetVideoElement = withAssets(
  "video" as any,
) as React.ForwardRefExoticComponent<
  React.VideoHTMLAttributes<HTMLVideoElement> &
    React.RefAttributes<HTMLVideoElement>
>;

export const AssetAudioElement = withAssets(
  "audio" as any,
) as React.ForwardRefExoticComponent<
  React.AudioHTMLAttributes<HTMLAudioElement> &
    React.RefAttributes<HTMLAudioElement>
>;
