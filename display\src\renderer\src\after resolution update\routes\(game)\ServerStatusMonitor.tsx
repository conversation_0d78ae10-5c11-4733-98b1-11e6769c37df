import { createFileRoute } from "@tanstack/react-router";
import React, { useEffect, useState } from "react";
import { useSocketIO } from "@renderer/contexts/SocketIOContext";

export const Route = createFileRoute("/(game)/ServerStatusMonitor")({
  component: ServerStatusMonitor,
});

function ServerStatusMonitor() {
  const {
    socket,
    isConnected,
    isServerDown,
    lastServerActivity,
    lastDataReceived,
    checkServerStatus,
  } = useSocketIO();
  const [lastChecked, setLastChecked] = useState(Date.now());

  // Check server status every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      checkServerStatus();
      setLastChecked(Date.now());
    }, 5000);

    return () => clearInterval(interval);
  }, [checkServerStatus]);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <h1 className="mb-6 text-3xl font-bold">Server Status Monitor</h1>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold">Connection Status</h2>
          <div className="mb-4 flex items-center">
            <div
              className={`mr-2 h-4 w-4 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`}
            ></div>
            <span>{isConnected ? "Connected" : "Disconnected"}</span>
          </div>

          <div className="mb-4 flex items-center">
            <div
              className={`mr-2 h-4 w-4 rounded-full ${isServerDown ? "bg-red-500" : "bg-green-500"}`}
            ></div>
            <span>{isServerDown ? "Server is DOWN" : "Server is UP"}</span>
          </div>

          <div className="mb-4">
            <span className="font-medium">Socket ID:</span>{" "}
            {socket?.id || "N/A"}
          </div>

          <div className="mb-4">
            <button
              onClick={checkServerStatus}
              className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
            >
              Check Server Status
            </button>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold">Activity Timestamps</h2>
          <div className="mb-4">
            <span className="font-medium">Last Server Activity:</span>{" "}
            {new Date(lastServerActivity).toLocaleTimeString()}
            <div className="text-xs text-gray-500">
              {Math.floor((Date.now() - lastServerActivity) / 1000)} seconds ago
            </div>
          </div>
          <div className="mb-4">
            <span className="font-medium">Last Data Received:</span>{" "}
            {new Date(lastDataReceived).toLocaleTimeString()}
            <div className="text-xs text-gray-500">
              {Math.floor((Date.now() - lastDataReceived) / 1000)} seconds ago
            </div>
          </div>
          <div className="mb-4">
            <span className="font-medium">Last Status Check:</span>{" "}
            {new Date(lastChecked).toLocaleTimeString()}
          </div>
          <div className="mt-6 mb-4">
            <div
              className={`rounded p-2 ${isServerDown ? "bg-red-100" : "bg-green-100"}`}
            >
              <span className="font-medium">Server Status:</span>
              {isServerDown ? (
                <span className="font-bold text-red-600"> OFFLINE</span>
              ) : (
                <span className="font-bold text-green-600"> ONLINE</span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 rounded-lg bg-white p-6 shadow-md">
        <h2 className="mb-4 text-xl font-semibold">Server Status History</h2>
        <p className="text-gray-600">
          This component monitors the server connection status and provides
          real-time updates on the server's availability. When the server goes
          down, the simulation mode will automatically activate to provide a
          seamless experience.
        </p>
      </div>
    </div>
  );
}
