/* Styles for game history entries */

.history-entry {
  position: relative;
  padding: 0.6vh 0;
  /* margin: 0.rem 0; */
  width: 100%;

}

/* Common styles for decorative borders */
.history-entry::after,
.history-entry:first-child::before {
  opacity: 50%;
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top:  98%;
  height: 0.5vh; /* Border width as specified */
  background: linear-gradient(
    to right,
    rgba(254, 241, 9, 0), /* Transparent yellow (same as head-active) */
    rgba(255, 255, 255, 0.8) 50%, /* Semi-opaque yellow */
    rgba(255, 255, 255, 0.8) 50%, /* Semi-opaque orange (same as tail-active) */
    rgba(254, 148, 13, 0) /* Transparent orange */
  );
  pointer-events: none; /* Ensure it doesn't interfere with clicks */
  z-index: 5; /* Ensure borders appear above content */
}

/* Bottom border for all entries */
.history-entry::after {
  bottom: 0;
}

/* Top border only for the first entry */
.history-entry:first-child::before {
  top: 0;
}

.middle-line {
  position: relative;
  display: grid;
  grid-template-columns: repeat(20, 1fr);
}

/* Create a visual break after the 10th number */
.middle-line > div:nth-child(10) {
  position: relative;
  margin-right: 1.3vh; /* Add space after the 10th number */
}

/* Styling for the divider after the 10th number */
.middle-line .bg-red-500 {
  position: absolute;
  right: -1vh;
  top: -15%;
  height: 134%;
  width: 0.4vh !important; /* Thinner line for elegance */
  background: rgba(243, 243, 243, 0.8);
  z-index: 6; /* Ensure borders appear above content */
}

.history-entry .middle-border {
  
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 5vh; /* Border width as specified */
  background: linear-gradient(
    to right,
    rgba(254, 241, 9, 0), /* Transparent yellow (same as head-active) */
    rgba(254, 241, 9, 0.8) 30%, /* Semi-opaque yellow */
    rgba(254, 148, 13, 0.8) 70%, /* Semi-opaque orange (same as tail-active) */
    rgba(254, 148, 13, 0) /* Transparent orange */
  );
  /* pointer-events: none; Ensure it doesn't interfere with clicks */
  z-index: 5; /* Ensure borders appear above content */
}
