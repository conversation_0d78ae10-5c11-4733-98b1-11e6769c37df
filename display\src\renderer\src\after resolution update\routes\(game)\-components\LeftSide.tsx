import { useMemo } from "react";

import { NumberPlate } from "./NumberContainer";
import { cn } from "@renderer/lib/utils";
import { NumberPlate2 } from "./NumberContainer2";

import draw9Img from "../../../assets/images/9draw.png";
import appLogoImg from "../../../assets/images/app-logo.png";

export const LeftSideBar = ({
  type,
  outcomes,
  roundNumber,
}: {
  outcomes: number[];
  roundNumber?: number;
  type?: string;
}) => {
  const { headCount, tailCount } = useMemo(() => {
    return outcomes.reduce(
      (counts, num) => {
        if (num <= 40) {
          counts.headCount++;
        } else {
          counts.tailCount++;
        }
        return counts;
      },
      { headCount: 0, tailCount: 0 },
    );
  }, [outcomes]);

  return (
    <>
      <div className="mt-[4vh] flex flex-col">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center">
            <div>
              <img
                crossOrigin="anonymous"
                className="drawTextImage"
                src={draw9Img}
                alt="draw text"
              />
            </div>
            <p
              style={{
                fontSize: "7.6vh",
                fontFamily: "Eurostib",
                lineHeight: 1,
                fontWeight: 900,
              }}
              className="text-shadow text-white"
            >
              {roundNumber}
            </p>
          </div>
          <div
            className={cn(
              "relative grid h-[7.5vh] w-[24vh] place-items-center bg-cover bg-center bg-no-repeat !text-black",
              headCount > tailCount
                ? "bg-[url('./assets/images/9head.png')]"
                : headCount === tailCount
                  ? "bg-[url('./assets/images/9even.png')]"
                  : "bg-[url('./assets/images/9red.png')] !text-red-700/20",
            )}
          >
            <p className="absolute top-1/2 left-1/2 -translate-1/2 text-[2.7vw] uppercase">
              {headCount === tailCount ? "Evens" : "Heads"}
            </p>
          </div>
        </div>
        <div className="numbers">
          {type === "static" ? (
            <NumberPlate2 outcomes={outcomes} />
          ) : (
            <NumberPlate outcomes={outcomes} />
          )}
          {/* <NumberPlate outcomes={outcomes} /> */}
        </div>
        <div
          className={cn(
            "relative grid h-[7.5vh] w-[24vh] place-items-center self-end bg-cover bg-center bg-no-repeat !text-black",
            tailCount > headCount
              ? "bg-[url('./assets/images/9tail.png')]"
              : headCount === tailCount
                ? "bg-[url('./assets/images/9even.png')]"
                : "bg-[url('./assets/images/9red.png')] !text-red-700/20",
          )}
        >
          <p className="absolute top-1/2 left-1/2 -translate-1/2 text-[2.7vw] uppercase">
            {headCount === tailCount ? "Evens" : "Tails"}
          </p>
        </div>
      </div>
      <img
        crossOrigin="anonymous"
        src={appLogoImg}
        alt="app logo"
        className="absolute -bottom-[0.5vh] z-50 h-[9.5vh] w-auto object-contain"
      />
    </>
  );
};
