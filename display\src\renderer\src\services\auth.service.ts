import { apiClient } from "@renderer/apiClient";
import { z } from "zod";

export const loginSchema = z.object({
  username: z.string(),
  password: z.string().min(4),
});

export default {
  getAuthInfo: async () => {
    const response = await apiClient.get(`/auth`);
    return response.data;
  },

  login: async (data: z.infer<typeof loginSchema>) => {
    const response = await apiClient.post(`/auth/login`, data);
    return response.data;
  },

  refreshToken: async () => {
    const response = await apiClient.get(`/auth/refresh-token`);
    return response.data;
  },

  logout: async () => {
    const response = await apiClient.post(`/auth/logout`);
    return response.data;
  },
};
