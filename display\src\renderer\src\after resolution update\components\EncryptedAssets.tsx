import React, { forwardRef } from "react";
import { useAsset } from "../hooks/useAsset";

interface AssetImageProps
  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Asset Image component that automatically handles embedded asset loading
 */
export const AssetImage = forwardRef<HTMLImageElement, AssetImageProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useAsset(src, { fallback });

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
      }
    }, [error, onLoadError]);

    if (loading) {
      return (
        <div
          className={`animate-pulse bg-gray-200 ${props.className || ""}`}
          style={props.style}
        >
          {/* Loading placeholder */}
        </div>
      );
    }

    if (!url) {
      return fallback ? (
        <img
          {...props}
          ref={ref}
          src={fallback}
          alt={props.alt || "Fallback image"}
        />
      ) : null;
    }

    return <img {...props} ref={ref} src={url} />;
  },
);

AssetImage.displayName = "AssetImage";

interface AssetVideoProps
  extends Omit<React.VideoHTMLAttributes<HTMLVideoElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Asset Video component that automatically handles embedded asset loading
 */
export const AssetVideo = forwardRef<HTMLVideoElement, AssetVideoProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useAsset(src, { fallback });
    const videoRef = React.useRef<HTMLVideoElement>(null);
    const [isReady, setIsReady] = React.useState(false);
    const [hasError, setHasError] = React.useState(false);

    // Combine refs
    React.useImperativeHandle(ref, () => videoRef.current!, []);

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
        setHasError(true);
      }
    }, [error, onLoadError]);

    // Handle video loading and playing more carefully
    React.useEffect(() => {
      const video = videoRef.current;
      if (!video || !url) {
        setIsReady(false);
        return;
      }

      console.log("🔧 Setting video src:", { src, url, videoSrc: video.src });
      setHasError(false);
      setIsReady(false);

      const handleLoadStart = () => {
        console.log("🎬 Video load started:", src, "actual src:", video.src);
        setIsReady(false);
      };

      const handleLoadedMetadata = () => {
        console.log("📊 Video metadata loaded:", src);
      };

      const handleCanPlay = () => {
        console.log("✅ Video can play:", src, "duration:", video.duration);
        setIsReady(true);

        // Only autoplay if explicitly requested and not controlled externally
        if (props.autoPlay && !video.hasAttribute("data-external-control")) {
          video.play().catch((e) => {
            console.error("❌ Video autoplay failed:", src, e);
            setHasError(true);
          });
        }
      };

      const handleCanPlayThrough = () => {
        console.log("🚀 Video can play through:", src);
      };

      const handleError = (e: Event) => {
        console.error("❌ Video error:", src, e);

        // Get detailed MediaError information
        const mediaError = video.error;
        let errorMessage = "Unknown video error";
        let errorCode = "UNKNOWN";

        if (mediaError) {
          switch (mediaError.code) {
            case MediaError.MEDIA_ERR_ABORTED:
              errorMessage = "Video loading was aborted";
              errorCode = "MEDIA_ERR_ABORTED";
              break;
            case MediaError.MEDIA_ERR_NETWORK:
              errorMessage = "Network error while loading video";
              errorCode = "MEDIA_ERR_NETWORK";
              break;
            case MediaError.MEDIA_ERR_DECODE:
              errorMessage =
                "Video decoding error - codec not supported or file corrupted";
              errorCode = "MEDIA_ERR_DECODE";
              break;
            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
              errorMessage = "Video format not supported";
              errorCode = "MEDIA_ERR_SRC_NOT_SUPPORTED";
              break;
          }
        }

        console.error("❌ Video error details:", {
          error: mediaError,
          errorCode,
          errorMessage,
          networkState: video.networkState,
          readyState: video.readyState,
          src: video.src,
          currentSrc: video.currentSrc,
        });

        setHasError(true);
        setIsReady(false);
        if (onLoadError) {
          onLoadError(`${errorCode}: ${errorMessage}`);
        }
      };

      const handlePlay = () => {
        console.log(
          "▶️ Video started playing:",
          src,
          "currentTime:",
          video.currentTime,
          "duration:",
          video.duration,
        );
      };

      const handlePause = () => {
        console.log("⏸️ Video paused:", src, "currentTime:", video.currentTime);
      };

      const handleEnded = () => {
        console.log("🏁 Video ended:", src, "duration:", video.duration);
      };

      const handleTimeUpdate = () => {
        // Log every 1 second to avoid spam
        if (Math.floor(video.currentTime) % 1 === 0) {
          console.log(
            "⏰ Video time update:",
            src,
            "currentTime:",
            Math.floor(video.currentTime),
            "duration:",
            Math.floor(video.duration || 0),
          );
        }
      };

      // Add all event listeners
      video.addEventListener("loadstart", handleLoadStart);
      video.addEventListener("loadedmetadata", handleLoadedMetadata);
      video.addEventListener("canplay", handleCanPlay);
      video.addEventListener("canplaythrough", handleCanPlayThrough);
      video.addEventListener("error", handleError);
      video.addEventListener("play", handlePlay);
      video.addEventListener("pause", handlePause);
      video.addEventListener("ended", handleEnded);
      video.addEventListener("timeupdate", handleTimeUpdate);

      // Force load the video
      video.load();

      return () => {
        video.removeEventListener("loadstart", handleLoadStart);
        video.removeEventListener("loadedmetadata", handleLoadedMetadata);
        video.removeEventListener("canplay", handleCanPlay);
        video.removeEventListener("canplaythrough", handleCanPlayThrough);
        video.removeEventListener("error", handleError);
        video.removeEventListener("play", handlePlay);
        video.removeEventListener("pause", handlePause);
        video.removeEventListener("ended", handleEnded);
        video.removeEventListener("timeupdate", handleTimeUpdate);
      };
    }, [url, src, props.autoPlay, onLoadError]);

    if (loading) {
      return (
        <div
          className={`flex animate-pulse items-center justify-center bg-gray-900 ${props.className || ""}`}
          style={props.style}
        >
          <div className="text-sm text-white">Loading video...</div>
        </div>
      );
    }

    if (hasError && fallback) {
      return <video {...props} ref={videoRef} src={fallback} />;
    }

    if (!url && !fallback) {
      return (
        <div
          className={`flex items-center justify-center bg-red-900 ${props.className || ""}`}
          style={props.style}
        >
          <div className="text-sm text-white">Video not available</div>
        </div>
      );
    }

    // Remove autoPlay from props to handle it manually
    const { autoPlay, ...videoProps } = props;
    return (
      <video
        {...videoProps}
        ref={videoRef}
        src={url || fallback}
        // Add attributes to improve video compatibility
        crossOrigin="anonymous"
        playsInline
        preload="metadata"
        style={{
          ...videoProps.style,
          opacity: isReady ? 1 : 0.5,
        }}
      />
    );
  },
);

AssetVideo.displayName = "AssetVideo";

interface AssetAudioProps
  extends Omit<React.AudioHTMLAttributes<HTMLAudioElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Asset Audio component that automatically handles embedded asset loading
 */
export const AssetAudio = forwardRef<HTMLAudioElement, AssetAudioProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useAsset(src, { fallback });
    const audioRef = React.useRef<HTMLAudioElement>(null);

    // Combine refs
    React.useImperativeHandle(ref, () => audioRef.current!, []);

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
      }
    }, [error, onLoadError]);

    // Handle audio loading and playing more carefully
    React.useEffect(() => {
      const audio = audioRef.current;
      if (!audio || !url) return;

      const handleLoadStart = () => {
        console.log("Audio load started:", src);
      };

      const handleCanPlay = () => {
        console.log("Audio can play:", src);
        if (props.autoPlay) {
          audio.play().catch((e) => {
            console.error("Audio autoplay failed:", src, e);
          });
        }
      };

      const handleError = (e: Event) => {
        console.error("Audio error:", src, e);
        if (onLoadError) {
          onLoadError("Audio failed to load");
        }
      };

      audio.addEventListener("loadstart", handleLoadStart);
      audio.addEventListener("canplay", handleCanPlay);
      audio.addEventListener("error", handleError);

      return () => {
        audio.removeEventListener("loadstart", handleLoadStart);
        audio.removeEventListener("canplay", handleCanPlay);
        audio.removeEventListener("error", handleError);
      };
    }, [url, src, props.autoPlay, onLoadError]);

    if (loading || !url) {
      return fallback ? (
        <audio {...props} ref={audioRef} src={fallback} />
      ) : null;
    }

    // Remove autoPlay from props to handle it manually
    const { autoPlay, ...audioProps } = props;
    return <audio {...audioProps} ref={audioRef} src={url} />;
  },
);

AssetAudio.displayName = "AssetAudio";

interface AssetBackgroundProps {
  src: string;
  fallback?: string;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onLoadError?: (error: string) => void;
}

/**
 * Component for embedded background images
 */
export const AssetBackground: React.FC<AssetBackgroundProps> = ({
  src,
  fallback,
  children,
  className = "",
  style = {},
  onLoadError,
}) => {
  const { url, loading, error } = useAsset(src, { fallback });

  React.useEffect(() => {
    if (error && onLoadError) {
      onLoadError(error);
    }
  }, [error, onLoadError]);

  const backgroundStyle: React.CSSProperties = {
    ...style,
    backgroundImage: url ? `url("${url}")` : undefined,
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
  };

  if (loading) {
    return (
      <div className={`animate-pulse bg-gray-200 ${className}`} style={style}>
        {children}
      </div>
    );
  }

  return (
    <div className={className} style={backgroundStyle}>
      {children}
    </div>
  );
};

/**
 * Higher-order component to wrap existing components with embedded asset support
 */
export function withAssets<T extends { src?: string }>(
  Component: React.ComponentType<T>,
) {
  return forwardRef<any, any>((props: any, ref: any) => {
    const { src, ...otherProps } = props;
    const { url, loading, error } = useAsset(src || null);
    const elementRef = React.useRef<any>(null);

    // Combine refs
    React.useImperativeHandle(ref, () => elementRef.current, []);

    // Enhanced logging for video elements
    React.useEffect(() => {
      const isVideoComponent =
        typeof Component === "string" && Component === "video";
      if (isVideoComponent && elementRef.current && url) {
        const video = elementRef.current;

        const handleLoadStart = () => {
          console.log("🎬 [withEncryptedAssets] Video load started:", src);
        };

        const handleCanPlay = () => {
          console.log("✅ [withEncryptedAssets] Video can play:", src);
        };

        const handleError = (e: Event) => {
          console.error("❌ [withEncryptedAssets] Video error:", src, e);
        };

        video.addEventListener("loadstart", handleLoadStart);
        video.addEventListener("canplay", handleCanPlay);
        video.addEventListener("error", handleError);

        return () => {
          video.removeEventListener("loadstart", handleLoadStart);
          video.removeEventListener("canplay", handleCanPlay);
          video.removeEventListener("error", handleError);
        };
      }
      return undefined;
    }, [url, src, Component]);

    const isVideoComponent =
      typeof Component === "string" && Component === "video";

    if (loading && isVideoComponent) {
      return React.createElement("div", {
        className: `flex animate-pulse items-center justify-center bg-gray-900 ${props.className || ""}`,
        style: props.style,
        children: React.createElement("div", {
          className: "text-sm text-white",
          children: "Loading video...",
        }),
      });
    }

    if (error && isVideoComponent) {
      console.error("❌ [withAssets] Failed to load video:", src, error);
    }

    return React.createElement(Component, {
      ...otherProps,
      ref: elementRef,
      src: url || src,
    });
  });
}

// Pre-wrapped common components with proper typing
export const AssetImg = withAssets(
  "img" as any,
) as React.ForwardRefExoticComponent<
  React.ImgHTMLAttributes<HTMLImageElement> &
    React.RefAttributes<HTMLImageElement>
>;

export const AssetVideoElement = withAssets(
  "video" as any,
) as React.ForwardRefExoticComponent<
  React.VideoHTMLAttributes<HTMLVideoElement> &
    React.RefAttributes<HTMLVideoElement>
>;

export const AssetAudioElement = withAssets(
  "audio" as any,
) as React.ForwardRefExoticComponent<
  React.AudioHTMLAttributes<HTMLAudioElement> &
    React.RefAttributes<HTMLAudioElement>
>;

// Keep backward compatibility
export const EncryptedImage = AssetImage;
export const EncryptedVideo = AssetVideo;
export const EncryptedAudio = AssetAudio;
export const EncryptedBackground = AssetBackground;
export const withEncryptedAssets = withAssets;
export const EncryptedImg = AssetImg;
export const EncryptedVideoElement = AssetVideoElement;
export const EncryptedAudioElement = AssetAudioElement;
