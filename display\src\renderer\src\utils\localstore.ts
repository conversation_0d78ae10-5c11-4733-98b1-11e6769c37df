// Cache for synchronous access
let tokenCache: string | null = null;
let isInitialized = false;

// Initialize the cache
const initializeCache = async () => {
  if (!isInitialized) {
    try {
      tokenCache = await window.api.authStore.get('_access_token_') || null;
      isInitialized = true;
    } catch (error) {
      console.error("Failed to initialize auth cache:", error);
    }
  }
};

// Initialize cache immediately
initializeCache();

export default {
  /**
   * Authenticate user by storing access token
   * @param token - JWT access token
   */
  authenticateUser: (token: string): void => {
    if (!token || typeof token !== "string") {
      throw new Error("Invalid token provided");
    }
    tokenCache = token;
    window.api.authStore.set('_access_token_', token);
  },

  /**
   * Check if user is authenticated - check if a token is saved in Electron Store
   * @returns {boolean} Authentication status
   */
  isUserAuthenticated: (): boolean => {
    return tokenCache !== null;
  },

  /**
   * Deauthenticate user by removing access token from Electron Store
   */
  deauthenticateUser: (): void => {
    tokenCache = null;
    window.api.authStore.delete('_access_token_');
  },

  /**
   * Get stored access token
   * @returns {string | null} Access token or null if not found
   */
  getToken: (): string | null => {
    return tokenCache;
  },
};
