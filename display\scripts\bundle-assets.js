const fs = require("fs");
const path = require("path");

// Directories
const PUBLIC_DIR = path.join(__dirname, "../public");
const OUTPUT_DIR = path.join(__dirname, "../src/main");

// Get all asset files recursively
function getAllAssets(dir, baseDir = dir) {
  const assets = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      assets.push(...getAllAssets(fullPath, baseDir));
    } else {
      // Only include specific asset types
      const ext = path.extname(item).toLowerCase();
      if (
        [
          ".png",
          ".jpg",
          ".jpeg",
          ".gif",
          ".mp4",
          ".mp3",
          ".wav",
          ".ogg",
          ".ttf",
          ".otf",
          ".woff",
          ".woff2",
        ].includes(ext)
      ) {
        const relativePath = path.relative(baseDir, fullPath);
        assets.push({
          originalPath: fullPath,
          relativePath: relativePath.replace(/\\/g, "/"), // Normalize path separators
          filename: item,
          extension: ext,
          size: stat.size,
        });
      }
    }
  }

  return assets;
}

// Get MIME type from file extension
function getMimeTypeFromExtension(extension) {
  const mimeTypes = {
    ".png": "image/png",
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".gif": "image/gif",
    ".mp4": "video/mp4",
    ".mp3": "audio/mpeg",
    ".wav": "audio/wav",
    ".ogg": "audio/ogg",
    ".ttf": "font/ttf",
    ".otf": "font/otf",
    ".woff": "font/woff",
    ".woff2": "font/woff2",
  };
  return mimeTypes[extension.toLowerCase()] || "application/octet-stream";
}

// Main bundling process
function bundleAssets() {
  console.log("📦 Starting asset bundling process...");

  // Get all assets
  const assets = getAllAssets(PUBLIC_DIR);
  console.log(`📁 Found ${assets.length} assets to bundle`);

  const manifest = {
    version: "1.0.0",
    timestamp: new Date().toISOString(),
    assets: {},
  };

  const assetBuffers = [];
  let currentOffset = 0;
  let successCount = 0;
  let errorCount = 0;
  let totalSize = 0;

  // Process each asset
  for (const asset of assets) {
    console.log(`📁 Processing: ${asset.relativePath}`);

    try {
      const data = fs.readFileSync(asset.originalPath);

      // Store asset info in manifest
      manifest.assets[asset.relativePath] = {
        offset: currentOffset,
        length: data.length,
        size: data.length,
        extension: asset.extension,
        mimeType: getMimeTypeFromExtension(asset.extension),
      };

      assetBuffers.push(data);
      currentOffset += data.length;
      totalSize += data.length;
      successCount++;

      console.log(`✅ Bundled: ${asset.relativePath} (${data.length} bytes)`);
    } catch (error) {
      errorCount++;
      console.error(
        `❌ Failed to read: ${asset.relativePath} - ${error.message}`,
      );
    }
  }

  // Combine all asset data into a single buffer
  console.log("📝 Creating asset bundle...");
  const bundleBuffer = Buffer.concat(assetBuffers);

  // Write bundle file
  const bundlePath = path.join(OUTPUT_DIR, "assets.bundle");
  fs.writeFileSync(bundlePath, bundleBuffer);

  // Write manifest file
  const manifestPath = path.join(OUTPUT_DIR, "assets-manifest.json");
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

  // Create TypeScript interface file
  const tsContent = `// Auto-generated file - DO NOT EDIT
// Generated on: ${manifest.timestamp}
// Total assets: ${Object.keys(manifest.assets).length}

export interface BundledAsset {
  offset: number;
  length: number;
  size: number;
  extension: string;
  mimeType: string;
}

export interface AssetsManifest {
  version: string;
  timestamp: string;
  assets: { [path: string]: BundledAsset };
}
`;

  const tsPath = path.join(OUTPUT_DIR, "assets-types.ts");
  fs.writeFileSync(tsPath, tsContent);

  console.log("\n📊 Bundling Summary:");
  console.log(`✅ Successfully bundled: ${successCount} files`);
  console.log(`❌ Failed to bundle: ${errorCount} files`);
  console.log(
    `📦 Total bundle size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`,
  );
  console.log(`📄 Bundle written to: ${bundlePath}`);
  console.log(`📄 Manifest written to: ${manifestPath}`);
  console.log(`📄 Types written to: ${tsPath}`);

  if (errorCount > 0) {
    process.exit(1);
  }

  console.log("🎉 Asset bundling completed successfully!");
  console.log("💡 Assets are now bundled in a single file!");
}

// Run the bundling process
if (require.main === module) {
  bundleAssets();
}

module.exports = { bundleAssets };
