import { ElectronAPI } from '@electron-toolkit/preload'

interface API {
  getAssetUrl: (assetPath: string) => string
  getEncryptedAssetUrl: (assetPath: string) => string
  authStore: {
    set: (key: string, value: any) => Promise<void>
    get: (key: string) => Promise<any>
    has: (key: string) => Promise<boolean>
    delete: (key: string) => Promise<void>
  }
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: API
  }
}
