import { app, shell, BrowserWindow, ipcMain } from "electron";
import { join } from "path";
import { electronApp, optimizer, is } from "@electron-toolkit/utils";
import icon from "../../resources/icon.png?asset";
import { embeddedAssetManager } from "./embedded-asset-manager";

// Store instance will be initialized asynchronously
let authStore: any = null;

// Initialize Electron Store asynchronously
const initializeStore = async () => {
  try {
    const ElectronStore = await import("electron-store");
    const StoreClass = ElectronStore.default;
    authStore = new StoreClass({
      name: "auth",
      watch: true
    });
    // Electron Store initialized successfully
  } catch (error) {
    console.error("❌ Failed to initialize Electron Store:", error);
  }
};

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    show: false,
    fullscreen: true,
    autoHideMenuBar: true,
    ...(process.platform === "linux" ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, "../preload/index.js"),
      sandbox: false,
      webSecurity: true,
    },
  });

  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });

  // Set Content Security Policy to allow embedded-asset protocol
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self' 'unsafe-inline' 'unsafe-eval' embedded-asset: data: blob:; " +
          "img-src 'self' 'unsafe-inline' embedded-asset: data: blob:; " +
          "media-src 'self' 'unsafe-inline' embedded-asset: data: blob:; " +
          "font-src 'self' 'unsafe-inline' embedded-asset: data: blob:; " +
          "style-src 'self' 'unsafe-inline' embedded-asset: data: blob:; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' embedded-asset: data: blob:; " +
          "connect-src 'self' ws: wss: http: https: embedded-asset: data: blob:;"
        ]
      }
    });
  });

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url);
    return { action: "deny" };
  });

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(join(__dirname, "../renderer/index.html"));
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId("com.electron");

  // Initialize Electron Store
  await initializeStore();

  // Set up IPC handlers for auth store
  ipcMain.handle('auth-store-set', (_event, key: string, value: any) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.set(key, value);
  });

  ipcMain.handle('auth-store-get', (_event, key: string) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.get(key);
  });

  ipcMain.handle('auth-store-has', (_event, key: string) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.has(key);
  });

  ipcMain.handle('auth-store-delete', (_event, key: string) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.delete(key);
  });

  // Initialize embedded asset manager
  if (embeddedAssetManager.initialize()) {
    embeddedAssetManager.registerProtocol();
  } else {
  }

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on("browser-window-created", (_, window) => {
    optimizer.watchWindowShortcuts(window);
  });

  // IPC test
  ipcMain.on("ping", () => console.log("pong"));

  createWindow();

  app.on("activate", function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
