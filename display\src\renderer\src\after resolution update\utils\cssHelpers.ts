import { getAssetCssUrl } from '../hooks/useAsset';

/**
 * Generate CSS styles with embedded asset URLs
 */
export const generateAssetStyles = () => {
  // Create a style element
  const styleElement = document.createElement('style');
  styleElement.id = 'encrypted-assets-styles';

  // Define the CSS with embedded asset URLs
  const css = `
    /* Font faces with embedded assets */
    @font-face {
      font-family: "Eurostib";
      src: url("embedded-asset://fonts/eurostib.ttf");
    }
    @font-face {
      font-family: "Eurostib-Pro";
      src: url("embedded-asset://fonts/eurostib-pro.otf");
    }
    @font-face {
      font-family: "Goodtimes";
      src: url("embedded-asset://fonts/good-times.otf");
    }

    /* Main App background */
    .App {
      width: 100vw;
      height: 100vh;
      background: ${getAssetCssUrl('images/vidbg.png')} no-repeat center center fixed;
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }

    /* Layout classes */
    .NumberAppContainer {
      display: flex;
      width: 100%;
    }
    .leftSideBar {
      position: relative;
      width: 58% !important;
      padding: 0.5rem 1.2vw 0 3.5vw;
    }
    .numbers {
      width: 100%;
    }
    .darkOrange {
      color: #f49119;
    }
    .text-white {
      color: #fff;
    }
    .Eurostib {
      font-family: "Eurostib";
    }
    .text-error {
      color: #ec3118;
      font-family: Goodtimes;
    }
    .text-center {
      text-align: center;
    }
    .uppercase {
      text-transform: uppercase;
    }

    .HourNumberImage {
      height: 5.6vw;
      object-fit: cover;
    }

    .drawTextImage {
      width: 15vw;
      height: 8vh;
      object-fit: cover;
    }
    .text-darkOrange {
      color: #f49119;
    }

    .info {
      height: 100vh;
      width: 42% !important;
      color: #fff;
      padding-top: 2.5rem;
      background: linear-gradient(210deg, #850c02 -29.09%, #310400 51.77%);
    }

    .App-header {
      background-color: #282c34;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: calc(10px + 2vmin);
      color: white;
    }

    .App-link {
      color: #09d3ac;
    }

    .about {
      margin-top: 10%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: calc(10px + 2vmin);
    }

    .keno {
      color: #fff;
      mix-blend-mode: soft-light !important;
      font-size: 4vw;
    }
    .candidateNumber {
      font-family: "Eurostib";
    }

    /* Background classes for different states */
    .bg-bgRedDark {
      background-image: ${getAssetCssUrl('images/red.png')};
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
      color: rgb(255, 26, 0) !important;
    }

    .head-active {
      background-image: ${getAssetCssUrl('images/yellow.png')};
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      color: #000 !important;
    }

    .tail-active {
      background-image: ${getAssetCssUrl('images/orange.png')};
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
      color: #000 !important;
    }

    .numberContainer {
      width: 100%;
    }

    .mt-1 {
      margin-top: 0.8rem;
    }

    .space-x-1 {
      margin-left: 0.25rem; /* 4px */
    }
    .space-x-2 {
      margin-left: 0.5rem; /* 8px */
    }
    .main {
      width: 100%;
    }
    .space-y-1 {
      margin-top: 0.25rem; /* 4px */
    }
    .w-full {
      width: 100%;
    }
    .text-black {
      color: #333;
    }
    .gap-10 {
      gap: 2.5rem; /* 40px */
    }
    .gap-x-2 {
      column-gap: 0.5rem !important; /* 8px */
    }
    .gap-x-1 {
      column-gap: 0.25rem !important; /* 8px */
    }
    .gap-10 {
      gap: 1.25rem; /* 20px */
    }
    .my-1 {
      margin-top: 0.6rem;
      margin-bottom: 0.6rem;
    }
    .flex {
      display: flex;
    }
    .items-center {
      align-items: center;
    }
    .justify-between {
      justify-content: space-between;
    }
    .shadow-md {
      box-shadow:
        0 4px 6px -1px rgb(0 0 0 / 0.1),
        0 2px 4px -2px rgb(0 0 0 / 0.1);
    }

    .opacity-60 {
      opacity: 0.6;
    }
    .rounded-md {
      border-radius: 0.575rem; /* 6px */
    }
    .rounded {
      border-radius: 0.25rem; /* 4px */
    }
    button {
      background-color: white;
      color: black;
      padding: 10px 20px;
      border-radius: 10px;
      font-size: 1.3rem;
      box-shadow:
        0px 8px 28px -6px rgba(24, 39, 75, 0.12),
        0px 18px 88px -4px rgba(24, 39, 75, 0.14);
      transition: all ease-in 0.1s;
      cursor: pointer;
      opacity: 0.9;
    }
    .px-5 {
      padding-left: 1.5rem; /* 24px */
      padding-right: 1.5rem; /* 24px */
    }
    .button-even {
      background-image: ${getAssetCssUrl('images/9even.png')};
      background-repeat: no-repeat;
      background-size: cover;
      padding: 0.5rem 3rem;
      text-align: center;
      margin-left: 5px;
    }
    .button-head {
      text-align: center;
      margin-left: 5px;
      color: black;
    }
    .button-tail {
      text-align: center;
      color: black;
    }

    .tailContainer {
      display: flex;
      justify-content: space-between;
      margin-top: 1rem;
    }

    /* Drawing video container */
    .drawingVideoContainer {
      position: relative;
      width: 39.77% !important;
      height: 100vh;
      background: ${getAssetCssUrl('images/last.png')} no-repeat center center fixed;
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
      z-index: 50;
      overflow: hidden;
    }

    .drawenNumbers {
      font-family: "Eurostib";
      color: white;
      position: absolute;
      top: 8%;
      right: 0%;
      font-weight: bold;
    }

    /* History page background */
    .history {
      width: 100vw;
      height: 100vh;
      background: ${getAssetCssUrl('images/historybg.png')} no-repeat center center fixed;
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
      overflow: hidden;
    }

    .text-shadow {
      text-shadow: -3px 0px 10px black;
    }

    .blink {
      animation: blink 1s infinite;
    }

    @keyframes blink {
      0% {
        opacity: 0;
      }
      50% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }

    .last-number {
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.8) !important;
      border: 2px solid white;
    }

    .animated-number {
      position: absolute !important;
      overflow: visible !important;
      transform-origin: center center !important;
      z-index: 9999 !important;
    }

    @keyframes bounce {
      0% { transform: scale(1.8); }
      15% { transform: scale(1.3); }
      30% { transform: scale(1.6); }
      45% { transform: scale(1.1); }
      55% { transform: scale(1.2); }
      65% { transform: scale(1.0); }
      70% { transform: scale(1.03); }
      80% { transform: scale(1.0); }
    }

    .animate-visibility {
      animation: smooth-disappear 1s infinite;
    }

    @keyframes smooth-disappear {
      0% { visibility: visible; }
      50% { visibility: hidden; }
      100% { visibility: visible; }
    }

    /* Dynamic background classes for head/tail/even states */
    .bg-head {
      background-image: ${getAssetCssUrl('images/9head.png')};
    }

    .bg-tail {
      background-image: ${getAssetCssUrl('images/9tail.png')};
    }

    .bg-even {
      background-image: ${getAssetCssUrl('images/9even.png')};
    }

    .bg-red {
      background-image: ${getAssetCssUrl('images/9red.png')};
    }
  `;

  styleElement.textContent = css;
  return styleElement;
};

/**
 * Initialize embedded asset CSS styles
 */
export const initializeAssetStyles = () => {
  // Remove existing asset styles if any
  const existingStyles = document.getElementById('embedded-assets-styles');
  if (existingStyles) {
    existingStyles.remove();
  }

  // Add new asset styles
  const styleElement = generateAssetStyles();
  document.head.appendChild(styleElement);
};

/**
 * Get embedded asset URL for use in inline styles
 */
export const getInlineAssetUrl = (assetPath: string): string => {
  // In development, try to use original asset
  if (process.env.NODE_ENV === 'development') {
    return assetPath.startsWith('/') ? assetPath : `/${assetPath}`;
  }

  // Use embedded asset protocol
  return window.api.getAssetUrl(assetPath);
};



// Keep backward compatibility
export const generateEncryptedStyles = generateAssetStyles;
export const initializeEncryptedStyles = initializeAssetStyles;
export const getInlineEncryptedAssetUrl = getInlineAssetUrl;
